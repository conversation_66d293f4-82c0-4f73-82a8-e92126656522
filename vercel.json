{"version": 2, "buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "framework": "nextjs", "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains"}]}], "redirects": [{"source": "/admin", "destination": "/organizations", "permanent": false}], "env": {"NODE_ENV": "production"}}