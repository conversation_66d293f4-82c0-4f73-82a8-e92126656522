{"name": "itglue-clone", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "build:prod": "NODE_ENV=production next build", "start:prod": "NODE_ENV=production next start", "export": "echo 'Export handled by output: export in next.config.js'", "analyze": "ANALYZE=true next build", "type-check": "tsc --noEmit", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js", "health-check": "node scripts/health-check.js"}, "dependencies": {"@microsoft/microsoft-graph-client": "^3.0.7", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.53.0", "dotenv": "^17.2.1", "lucide-react": "^0.536.0", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.4.5", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "^5"}}