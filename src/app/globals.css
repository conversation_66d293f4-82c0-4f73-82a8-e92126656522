@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Global Search Spotlight Effects */
@keyframes spotlight-pulse {
  0%, 100% {
    opacity: 0.1;
    transform: scale(1);
  }
  50% {
    opacity: 0.2;
    transform: scale(1.1);
  }
}

@keyframes spotlight-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

.spotlight-bg {
  animation: spotlight-pulse 4s ease-in-out infinite;
}

.spotlight-float {
  animation: spotlight-float 6s ease-in-out infinite;
}

/* Enhanced backdrop blur for better spotlight effect */
.backdrop-blur-spotlight {
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
}

/* Smooth transitions for search results */
.search-result-item {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-result-item:hover {
  transform: translateY(-1px);
}

.search-result-selected {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.3);
}
